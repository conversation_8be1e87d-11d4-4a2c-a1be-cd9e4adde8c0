'use client';

import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation';
import { ArrowLeftIcon, CheckIcon, SparklesIcon } from '@heroicons/react/24/outline';
import { useUser } from '@auth0/nextjs-auth0/client';
import { useForm } from 'react-hook-form';
import toast from 'react-hot-toast';

import { Bill } from '../../../../types';
import { billApi, billActionApi, BillActionData, BillActionSubmitRequest, MessagePreviewRequest } from '../../../../services/apiClient';
import LoginButton from '../../../../components/auth/LoginButton';
import { useFormStatePreservation } from '../../../../hooks/useFormStatePreservation';
import { BillLifecycleTracker } from '../../../../components/bills/BillLifecycleTracker';
import { SummaryVersionTracker } from '../../../../components/bills/SummaryVersionTracker';
import { ValuesAnalysisTags, ValuesAnalysisSummary } from '../../../../components/bills/ValuesAnalysisTags';

interface BillActionFormData {
  stance: 'support' | 'oppose' | 'amend';
  selected_reasons: string[];
  custom_reasons: string[];
  personal_stories: string;
  custom_message: string;
  first_name: string;
  last_name: string;
  zip_code: string;
  email: string;
  address: string;
  city: string;
  state: string;
}

interface MessagePreviewData {
  representatives: unknown[];
  personalized_messages: unknown[];
  stance: string;
  selected_reasons: string[];
  custom_reasons: string[];
}

export default function BillActionPage() {
  const params = useParams();
  const router = useRouter();
  const { user, isLoading: isAuthLoading } = useUser();
  
  const [bill, setBill] = useState<Bill | null>(null);
  const [billActionData, setBillActionData] = useState<BillActionData | null>(null);
  const [isLoadingData, setIsLoadingData] = useState(true);
  const [summaryVersions, setSummaryVersions] = useState<any[]>([]);
  const [statusHistory, setStatusHistory] = useState<any[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [currentStep, setCurrentStep] = useState<'stance' | 'reasons' | 'contact' | 'ai_generation' | 'edit_and_send'>('stance');
  
  // Debug step changes
  React.useEffect(() => {
    console.log('📍 Step changed to:', currentStep);
  }, [currentStep]);

  // CRITICAL: Prevent auto-submission during step transitions and React rendering
  const [isFormSubmissionBlocked, setIsFormSubmissionBlocked] = useState(false);
  const [renderStabilityTimer, setRenderStabilityTimer] = useState<NodeJS.Timeout | null>(null);
  const [userExplicitlyClickedSubmit, setUserExplicitlyClickedSubmit] = useState(false);

  useEffect(() => {
    // Reset explicit click flag whenever step changes
    setUserExplicitlyClickedSubmit(false);
    
    if (currentStep === 'edit_and_send') {
      console.log('🚨 BLOCKING: Preventing all form submissions during edit_and_send step setup');
      setIsFormSubmissionBlocked(true);

      // Clear any existing timer
      if (renderStabilityTimer) {
        clearTimeout(renderStabilityTimer);
      }

      // Set a longer delay to ensure React rendering is completely stable
      const timer = setTimeout(() => {
        console.log('🚨 UNBLOCKING: Allowing form submissions after React rendering stabilizes');
        setIsFormSubmissionBlocked(false);
      }, 500); // Increased to 500ms

      setRenderStabilityTimer(timer);

      return () => {
        if (timer) clearTimeout(timer);
      };
    } else {
      // Reset blocking for other steps
      setIsFormSubmissionBlocked(false);
    }
  }, [currentStep]);
  const [messagePreview, setMessagePreview] = useState<MessagePreviewData | null>(null);
  const [isLoadingPreview, setIsLoadingPreview] = useState(false);
  const [aiProgress, setAiProgress] = useState(0);
  const [aiProgressMessage, setAiProgressMessage] = useState('');
  const [customReasonInput, setCustomReasonInput] = useState('');

  const form = useForm<BillActionFormData>({
    mode: 'onChange',
    defaultValues: {
      stance: undefined,
      selected_reasons: [],
      custom_reasons: [],
      personal_stories: '',
      custom_message: '',
      first_name: '',
      last_name: '',
      zip_code: '',
      email: '',
      address: '',
      city: '',
      state: ''
    }
  });

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
    setValue,
    getValues
  } = form;

  // Add form state preservation for login flow
  const billId = params?.id as string;
  const { saveFormState } = useFormStatePreservation({
    form,
    storageKey: `bill-action-form-${billId}`,
    preserveOnAuth: true,
    excludeFields: ['email'] // Don't persist email for privacy
  });

  const watchedStance = watch('stance');
  const watchedReasons = watch('selected_reasons');
  const watchedCustomReasons = watch('custom_reasons');
  const watchedZipCode = watch('zip_code');

  // Load bill and bill action data
  useEffect(() => {
    const loadData = async () => {
      if (params.id) {
        try {
          setIsLoadingData(true);

          // Load bill data, action data, timeline data, and values analysis
          const [billData, actionData, summaryVersionsData, statusHistoryData, valuesAnalysisData, valuesTagsData] = await Promise.all([
            billApi.getBillById(params.id as string),
            billActionApi.getBillActionData(params.id as string),
            billApi.getBillSummaryVersions(params.id as string).catch(() => []), // Graceful fallback
            billApi.getBillStatusHistory(params.id as string).catch(() => []), // Graceful fallback
            billApi.getBillValuesAnalysis(params.id as string).catch(() => null), // Graceful fallback
            billApi.getBillValuesTags(params.id as string).catch(() => []) // Graceful fallback
          ]);

          // Merge values analysis data into bill object
          const enrichedBillData = {
            ...billData,
            values_analysis: valuesAnalysisData,
            values_tags: valuesTagsData
          };

          setBill(enrichedBillData);
          setBillActionData(actionData);
          setSummaryVersions(summaryVersionsData);
          setStatusHistory(statusHistoryData);
        } catch (error) {
          console.error('Failed to load data:', error);
          toast.error('Failed to load bill information. Please try again.');
        } finally {
          setIsLoadingData(false);
        }
      }
    };

    loadData();
  }, [params.id]);

  const handleStanceSelect = (stance: 'support' | 'oppose' | 'amend') => {
    setValue('stance', stance);
    setValue('selected_reasons', []); // Reset reasons when stance changes
    setCurrentStep('reasons');
  };

  const handleNextStep = () => {
    console.log('🔄 handleNextStep called, current step:', currentStep);
    switch (currentStep) {
      case 'stance':
        console.log('🔄 Moving from stance → reasons');
        setCurrentStep('reasons');
        break;
      case 'reasons':
        console.log('🔄 Moving from reasons → contact');
        setCurrentStep('contact');
        break;
      case 'contact':
        console.log('🔄 Moving from contact → ai_generation');
        setCurrentStep('ai_generation');
        break;
      case 'ai_generation':
        console.log('🔄 Moving from ai_generation → edit_and_send');
        setCurrentStep('edit_and_send');
        break;
    }
  };

  const handlePrevStep = () => {
    switch (currentStep) {
      case 'reasons':
        setCurrentStep('stance');
        break;
      case 'contact':
        setCurrentStep('reasons');
        break;
      case 'ai_generation':
        setCurrentStep('contact');
        break;
      case 'edit_and_send':
        setCurrentStep('ai_generation');
        break;
    }
  };

  const handleReasonToggle = (reason: string) => {
    const currentReasons = getValues('selected_reasons');
    const newReasons = currentReasons.includes(reason)
      ? currentReasons.filter(r => r !== reason)
      : [...currentReasons, reason];
    setValue('selected_reasons', newReasons);
  };

  const handleAddCustomReason = () => {
    if (!customReasonInput.trim()) return;
    const currentCustomReasons = getValues('custom_reasons');
    const newCustomReasons = [...currentCustomReasons, customReasonInput.trim()];
    setValue('custom_reasons', newCustomReasons);
    setCustomReasonInput('');
  };

  // Auto-trigger AI generation when we reach ai_generation step
  useEffect(() => {
    if (currentStep === 'ai_generation' && !isLoadingPreview && !messagePreview && bill) {
      // For signed-in users, trigger immediately using their saved address
      if (user && user.user_metadata?.zip_code) {
        handlePreviewMessage();
      }
      // For guest users, trigger when they have entered valid contact info
      else {
        const formData = getValues();
        if (formData.zip_code && /^\d{5}(-\d{4})?$/.test(formData.zip_code) && 
            formData.email && formData.address && formData.city && formData.state &&
            formData.first_name && formData.last_name) {
          handlePreviewMessage();
        }
      }
    }
  }, [currentStep, isLoadingPreview, messagePreview, user, bill]);

  // Pre-populate AI message when messagePreview is available
  // CRITICAL: Only set the value BEFORE reaching edit_and_send step to prevent auto-submission
  useEffect(() => {
    if (messagePreview && messagePreview.personalized_messages?.[0]?.body && currentStep !== 'edit_and_send') {
      const currentMessage = getValues('custom_message');
      // Only set if the field is empty (initial load)
      if (!currentMessage) {
        console.log('🔄 Setting custom_message value from messagePreview (step:', currentStep, ')');
        let messageToUse = messagePreview.personalized_messages[0].body;

        // If multiple messages, create a generic greeting version
        if (messagePreview.personalized_messages.length > 1) {
          // Replace specific "Dear Senator/Representative [Name]" with generic greeting
          messageToUse = messageToUse
            .replace(/Dear (Senator|Representative) [^,]+,/, 'Dear Representatives,')
            .replace(/Dear (Sen\.|Rep\.) [^,]+,/, 'Dear Representatives,');
        }

        setValue('custom_message', messageToUse);
        console.log('🔄 custom_message value set');
      }
    } else if (currentStep === 'edit_and_send') {
      console.log('🚨 BLOCKED: setValue prevented on edit_and_send step to avoid auto-submission');
    }
  }, [messagePreview, setValue, getValues, currentStep]);

  const handlePreviewMessage = async (customZipCode?: string) => {
    if (!bill) {
      toast.error('Bill information not loaded');
      return;
    }

    setIsLoadingPreview(true);
    setAiProgress(0);
    setAiProgressMessage('Looking up your representatives...');

    // Simulate progress updates to keep user engaged
    const progressInterval = setInterval(() => {
      setAiProgress(prev => {
        if (prev < 90) {
          const increment = Math.random() * 15 + 5; // Random increment between 5-20%
          const newProgress = Math.min(prev + increment, 90);

          // Update progress message based on progress
          if (newProgress < 30) {
            setAiProgressMessage('Looking up your representatives...');
          } else if (newProgress < 60) {
            setAiProgressMessage('Analyzing bill content...');
          } else if (newProgress < 85) {
            setAiProgressMessage('Crafting personalized messages...');
          } else {
            setAiProgressMessage('Finalizing your message...');
          }

          return newProgress;
        }
        return prev;
      });
    }, 300); // Update every 300ms for smooth progress

    try {
      const formData = getValues();

      // Use custom zip code if provided, otherwise try user's saved address, fallback to form data
      let zipCode = customZipCode || formData.zip_code;
      if (!zipCode && user?.user_metadata?.zip_code) {
        zipCode = user.user_metadata.zip_code;
        setValue('zip_code', zipCode); // Update form with user's saved zip
      }

      if (!zipCode) {
        throw new Error('ZIP code is required to find your representatives');
      }

      const previewData: MessagePreviewRequest = {
        bill_id: bill.id,
        stance: formData.stance,
        selected_reasons: formData.selected_reasons,
        custom_reasons: formData.custom_reasons,
        personal_stories: formData.personal_stories,
        first_name: formData.first_name || user?.given_name || '',
        last_name: formData.last_name || user?.family_name || '',
        zip_code: zipCode
      };

      // Debug logging to verify request data
      console.log('Sending preview request:', previewData);
      console.log('ZIP code being sent:', zipCode);
      console.log('Form data:', formData);

      // First, test direct officials lookup to see if we get real representatives
      try {
        const officialsTest = await billActionApi.lookupOfficials(zipCode);
        console.log('Direct officials lookup result:', officialsTest);
      } catch (officialsError) {
        console.log('Direct officials lookup failed:', officialsError);
      }

      const result = await billActionApi.previewMessage(previewData);

      // Debug the actual result structure
      console.log('Preview message result:', result);
      console.log('Representatives in result:', result.representatives);
      console.log('Personalized messages:', result.personalized_messages);

      // Complete the progress and set result immediately
      clearInterval(progressInterval);
      setAiProgress(100);
      setAiProgressMessage('Message ready!');

      // Brief delay to show completion before resetting
      setTimeout(() => {
        setMessagePreview(result);

        // CRITICAL: Set the custom_message value immediately when preview is ready
        // This happens BEFORE moving to edit_and_send step to prevent auto-submission
        if (result.personalized_messages?.[0]?.body) {
          console.log('🔄 Setting custom_message immediately after AI generation');
          let messageToUse = result.personalized_messages[0].body;

          // If multiple messages, create a generic greeting version
          if (result.personalized_messages.length > 1) {
            messageToUse = messageToUse
              .replace(/Dear (Senator|Representative) [^,]+,/, 'Dear Representatives,')
              .replace(/Dear (Sen\.|Rep\.) [^,]+,/, 'Dear Representatives,');
          }

          setValue('custom_message', messageToUse);
          console.log('🔄 custom_message set during AI generation step');
        }

        setAiProgress(0);
        setAiProgressMessage('');
        setIsLoadingPreview(false);
      }, 800);

    } catch (error) {
      console.error('Failed to preview message:', error);

      // Clear interval and reset progress
      clearInterval(progressInterval);
      setAiProgress(0);
      setAiProgressMessage('');
      setIsLoadingPreview(false);

      if (error.message?.includes('timeout')) {
        toast.error('AI personalization is taking longer than expected. Please try again.');
      } else {
        toast.error('Failed to generate message preview. Please try again.');
      }
    }
  };

  const onFormSubmit = async (data: BillActionFormData) => {
    console.log('🚨 onFormSubmit called! Current step:', currentStep);
    console.trace('Form submit trace');
    
    // Reset the flag immediately to prevent multiple submissions
    setUserExplicitlyClickedSubmit(false);
    
    // SAFETY CHECK: Only allow form submission from edit_and_send step
    if (currentStep !== 'edit_and_send') {
      console.log('🚨 BLOCKED: Form submission attempted from wrong step:', currentStep);
      toast.error('Please complete all steps before sending your message.');
      return;
    }
    
    if (!bill) {
      toast.error('Bill information not loaded');
      return;
    }

    try {
      setIsSubmitting(true);

      const submitData: BillActionSubmitRequest = {
        bill_id: bill.id,
        stance: data.stance,
        selected_reasons: data.selected_reasons,
        custom_reasons: data.custom_reasons || [],
        personal_stories: data.personal_stories || undefined,
        custom_message: data.custom_message || undefined,
        first_name: data.first_name,
        last_name: data.last_name,
        zip_code: data.zip_code,
        email: data.email,
        address: data.address || undefined,
        city: data.city || undefined,
        state: data.state || undefined
      };

      // CRITICAL FIX: DO NOT automatically send messages!
      // Instead, show a confirmation dialog or redirect to a confirmation page
      console.log('🚨 BLOCKING AUTOMATIC SEND: User must explicitly confirm sending messages');
      
      // For now, show an alert to prevent automatic sending
      const userConfirmed = window.confirm(
        'Are you sure you want to send this message to your representatives?\n\n' +
        'This action will send your message directly to their offices.'
      );
      
      if (!userConfirmed) {
        console.log('🚨 USER CANCELLED: Message sending cancelled by user');
        toast.info('Message sending cancelled');
        setIsSubmitting(false);
        return;
      }
      
      const result = await billActionApi.submitBillAction(submitData);

      if (result.success) {
        // NEW: Check if this requires user confirmation (message prepared but not sent)
        if (result.requires_user_confirmation || result.status === 'PENDING') {
          const readyToContact = result.officials_ready_to_contact || 0;
          toast.success(`Your message is prepared and ready to send to ${readyToContact} representative${readyToContact > 1 ? 's' : ''}!`);
          console.log('🚨 MESSAGE PREPARED - User must confirm to actually send');
          
          // For now, still require explicit confirmation since this is critical
          // TODO: Show a proper confirmation UI instead of window.confirm
        } else if (result.action_network_embed?.iframe_url && result.delivery_summary?.requires_user_completion) {
          // Show Action Network embed for user to complete
          toast.success('Your message is ready! Please complete the form to send it to your representatives.');
          // TODO: Show Action Network iframe or redirect to iframe_url
          window.open(result.action_network_embed.iframe_url, '_blank');
        } else {
          // This should now rarely happen since we're not auto-sending
          const totalMessages = result.officials_contacted || result.delivery_summary?.total_targets || 0;
          toast.success(`Successfully sent your message to ${totalMessages} representative${totalMessages > 1 ? 's' : ''}!`);
        }
        
        // Always redirect back to bills page after short delay
        setTimeout(() => {
          router.push('/bills');
        }, 2000);
      } else {
        toast.error('Failed to send your message. Please try again.');
      }

    } catch (error) {
      console.error('Failed to submit bill action:', error);
      toast.error('Failed to send your message. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const getAvailableReasons = (): string[] => {
    if (!billActionData) return [];

    switch (watchedStance) {
      case 'support':
        return billActionData.support_reasons || [];
      case 'oppose':
        return billActionData.oppose_reasons || [];
      case 'amend':
        return billActionData.amend_reasons || [];
      default:
        return [];
    }
  };

  if (isLoadingData) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="flex items-center gap-3">
          <svg className="animate-spin h-8 w-8 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          <span className="text-lg text-gray-600">Loading bill information...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-blue-50">
      {/* Page Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <button
                onClick={() => router.back()}
                className="p-2 rounded-full text-gray-400 hover:text-gray-600 hover:bg-gray-100 transition-colors focus-ring"
              >
                <ArrowLeftIcon className="h-5 w-5" />
              </button>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Take Action</h1>
                <p className="text-sm text-gray-600">Make your voice heard on this legislation</p>
              </div>
            </div>

            {/* Progress Indicator */}
            <div className="hidden md:flex items-center gap-4">
              {[
                { key: 'stance', label: 'Position', icon: '🎯' },
                { key: 'reasons', label: 'Reasons', icon: '💭' },
                { key: 'contact', label: 'Contact', icon: '📍' },
                { key: 'ai_generation', label: 'Generate', icon: '✨' },
                { key: 'edit_and_send', label: 'Send', icon: '📤' }
              ].map((step, index) => {
                const stepIndex = ['stance', 'reasons', 'contact', 'ai_generation', 'edit_and_send'].indexOf(currentStep);
                const isCompleted = index < stepIndex;
                const isCurrent = currentStep === step.key;

                return (
                  <div key={step.key} className="flex items-center">
                    <div className={`flex items-center justify-center w-8 h-8 rounded-full text-sm font-medium transition-all ${
                      isCurrent
                        ? 'bg-blue-600 text-white shadow-lg'
                        : isCompleted
                        ? 'bg-green-500 text-white'
                        : 'bg-gray-200 text-gray-600'
                    }`}>
                      {isCompleted ? (
                        <CheckIcon className="w-4 h-4" />
                      ) : (
                        <span className="text-xs">{step.icon}</span>
                      )}
                    </div>
                    <span className={`ml-2 text-sm font-medium ${
                      isCurrent ? 'text-blue-600' : isCompleted ? 'text-green-600' : 'text-gray-500'
                    }`}>
                      {step.label}
                    </span>
                    {index < 4 && (
                      <div className={`w-8 h-0.5 mx-3 ${
                        isCompleted ? 'bg-green-500' : 'bg-gray-200'
                      }`} />
                    )}
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 mt-8">
        <div className="grid grid-cols-1 lg:grid-cols-12 gap-8">
          {/* Left Column - Bill Information (Sticky) */}
          {currentStep === 'stance' && (
            <div className="lg:col-span-5">
              <div className="sticky top-24">
                <BillInfoCard
                  bill={bill}
                  summaryVersions={summaryVersions}
                  statusHistory={statusHistory}
                />
              </div>
            </div>
          )}

          {/* Right Column - Action Flow */}
          <div className={currentStep === 'stance' ? 'lg:col-span-7' : 'lg:col-span-12'}>
            <form
              onSubmit={(e) => {
                console.log('🚨🚨🚨 CRITICAL: ALL FORM SUBMISSIONS ARE BLOCKED');
                console.log('🚨 Form submission attempted but will be blocked');
                console.log('🚨 Event:', e);
                console.log('🚨 Current step:', currentStep);
                e.preventDefault();
                e.stopPropagation();
                console.log('🚨 Form submission BLOCKED - Use button click instead');
                return false;
              }}
              onKeyDown={(e) => {
                // CRITICAL: Prevent ALL Enter key form submissions
                if (e.key === 'Enter') {
                  console.log('🚨 BLOCKED: Enter key form submission prevented on step:', currentStep);
                  console.log('🚨 Users must use the button to submit');
                  e.preventDefault();
                  e.stopPropagation();
                }
              }}
              className="bg-white rounded-2xl shadow-sm border border-gray-200 overflow-hidden"
            >
              {/* Step Content */}
              <div className="p-8">
                {currentStep === 'stance' && (
                  <StanceSelectionStep
                    watchedStance={watchedStance}
                    onStanceSelect={handleStanceSelect}
                  />
                )}

                {currentStep === 'reasons' && (
                  <ReasonsStep
                    watchedStance={watchedStance}
                    watchedReasons={watchedReasons}
                    watchedCustomReasons={watchedCustomReasons}
                    customReasonInput={customReasonInput}
                    setCustomReasonInput={setCustomReasonInput}
                    availableReasons={getAvailableReasons()}
                    onReasonToggle={handleReasonToggle}
                    onAddCustomReason={handleAddCustomReason}
                    register={register}
                  />
                )}

                {currentStep === 'contact' && (
                  <ContactStep
                    user={user}
                    register={register}
                    errors={errors}
                  />
                )}

                {currentStep === 'ai_generation' && (
                  <AIGenerationStep
                    isLoadingPreview={isLoadingPreview}
                    aiProgress={aiProgress}
                    aiProgressMessage={aiProgressMessage}
                    messagePreview={messagePreview}
                  />
                )}

                {currentStep === 'edit_and_send' && messagePreview && (
                  <EditAndSendStep
                    messagePreview={messagePreview}
                    register={register}
                    errors={errors}
                    user={user}
                    isSubmitting={isSubmitting}
                    watchedStance={watchedStance}
                    watchedReasons={watchedReasons}
                    watchedCustomReasons={watchedCustomReasons}
                    getValues={getValues}
                    setValue={setValue}
                  />
                )}
              </div>

              {/* Navigation Footer */}
              <div className="border-t border-gray-200 bg-gray-50 px-8 py-6">
                <div className="flex items-center justify-between">
                  <button
                    type="button"
                    onClick={handlePrevStep}
                    disabled={currentStep === 'stance'}
                    className={`btn-secondary ${
                      currentStep === 'stance' ? 'opacity-50 cursor-not-allowed' : ''
                    }`}
                  >
                    ← Previous
                  </button>

                  {currentStep === 'edit_and_send' ? (
                    <button
                      type="button"
                      disabled={isSubmitting|| isFormSubmissionBlocked}
                      className={`btn-primary px-8 ${
                        (isSubmitting || isFormSubmissionBlocked) ? 'bg-gray-400 cursor-not-allowed' : 'bg-green-600 hover:bg-green-700'
                      }`}
                      onFocus={() => console.log('🎯 Send button focused')}
                      onClick={async (e) => {
                        console.log('🎯🎯🎯 SEND BUTTON CLICKED - USER INTERACTION');
                        console.log('🎯 Click event:', e);
                        console.log('🎯 Event isTrusted:', e.isTrusted);
                        console.log('🎯 Event detail:', e.detail);
                        
                        // Prevent any default behavior
                        e.preventDefault();
                        e.stopPropagation();
                        
                        // CRITICAL: Only proceed if user is on the right step
                        if (currentStep !== 'edit_and_send') {
                          console.log('🚨 BLOCKED: Not on edit_and_send step');
                          toast.error('Please complete all steps before sending your message.');
                          return;
                        }
                        
                        // CRITICAL: Only proceed if not currently submitting
                        if (isSubmitting) {
                          console.log('🚨 BLOCKED: Already submitting');
                          return;
                        }
                        
                        // CRITICAL: Only proceed if rendering is stable
                        if (isFormSubmissionBlocked) {
                          console.log('🚨 BLOCKED: Form submission blocked due to React rendering');
                          return;
                        }
                        
                        console.log('🎯 ✅ PROCEEDING WITH FORM SUBMISSION - User explicitly clicked');
                        
                        // Get form data using React Hook Form
                        const formData = getValues();
                        console.log('🎯 Form data retrieved:', formData);
                        
                        // Call the submission handler directly
                        await onFormSubmit(formData);
                      }}
                      onMouseDown={() => console.log('🎯 Send button mouse down')}
                      onMouseUp={() => console.log('🎯 Send button mouse up')}
                    >
                      {isSubmitting ? 'Preparing Message...' : isFormSubmissionBlocked ? 'Loading...' : '📝 Review & Send Message'}
                    </button>
                  ) : (
                    <button
                      type="button"
                      onClick={handleNextStep}
                      disabled={currentStep === 'stance' && !watchedStance}
                      className={`btn-primary px-8 ${
                        currentStep === 'stance' && !watchedStance ? 'opacity-50 cursor-not-allowed' : ''
                      }`}
                    >
                      Continue →
                    </button>
                  )}
                </div>
              </div>
            </form>
          </div>
        </div>
      </main>
    </div>
  );
}

// Bill Information Card Component
function BillInfoCard({
  bill,
  summaryVersions,
  statusHistory
}: {
  bill: Bill | null;
  summaryVersions: any[];
  statusHistory: any[];
}) {
  if (!bill) {
    return (
      <div className="bg-white rounded-2xl shadow-sm border border-gray-200 overflow-hidden">
        <div className="animate-pulse">
          <div className="h-24 bg-gradient-to-r from-gray-300 to-gray-400"></div>
          <div className="p-6 space-y-4">
            <div className="h-6 bg-gray-200 rounded w-3/4"></div>
            <div className="space-y-2">
              <div className="h-4 bg-gray-200 rounded"></div>
              <div className="h-4 bg-gray-200 rounded w-5/6"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header Card */}
      <div className="bg-white rounded-2xl shadow-sm border border-gray-200 overflow-hidden">
        <div className="bg-gradient-to-r from-blue-600 to-purple-600 px-6 py-4">
          <h2 className="heading-2 text-white">{bill.bill_number}</h2>
          <p className="text-blue-100 mt-1 font-medium">{bill.title}</p>
        </div>
        
        <div className="p-6 space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <span className="text-sm font-medium text-gray-500">Status</span>
              <p className="text-gray-900 font-medium capitalize">{bill.status}</p>
            </div>
            <div>
              <span className="text-sm font-medium text-gray-500">Chamber</span>
              <p className="text-gray-900 font-medium capitalize">{bill.chamber}</p>
            </div>
          </div>
          
          <div className="grid grid-cols-2 gap-4">
            <div>
              <span className="text-sm font-medium text-gray-500">Session</span>
              <p className="text-gray-900">{bill.session_year}</p>
            </div>
            <div>
              <span className="text-sm font-medium text-gray-500">State</span>
              <p className="text-gray-900 uppercase">{bill.state}</p>
            </div>
          </div>
        </div>
      </div>



      {/* Values Analysis */}
      {(bill.values_analysis || (bill.values_tags && bill.values_tags.length > 0)) && (
        <BillDetailCard
          title="Impact Assessment"
          icon="🎯"
          bgColor="bg-purple-50"
          borderColor="border-purple-200"
          iconBg="bg-purple-100"
          titleColor="text-purple-900"
          textColor="text-purple-800"
        >
          <div className="space-y-4">
            <p className="text-sm text-purple-700 mb-4">
              This bill has been analyzed for its potential impact on democratic processes,
              civil rights, and environmental considerations.
            </p>

            {bill.values_analysis && (
              <ValuesAnalysisSummary analysis={bill.values_analysis} />
            )}

            {bill.values_tags && bill.values_tags.length > 0 && (
              <div>
                <h5 className="text-sm font-medium text-purple-800 mb-2">Key Considerations</h5>
                <ValuesAnalysisTags
                  tags={bill.values_tags}
                  variant="detailed"
                />
              </div>
            )}

            <p className="text-xs text-purple-600 italic mt-3">
              Analysis uses neutral, factual criteria to assess potential impacts.
              All assessments are reviewed by our policy team.
            </p>
          </div>
        </BillDetailCard>
      )}

      {/* Legislative Progress - Keep location, restore original design */}
      <BillLifecycleTracker
        currentStatus={bill.status}
        statusHistory={statusHistory}
        className="mt-6"
      />

      {/* Quick Summary - Shows TL;DR content but keeps original styling */}
      <BillDetailCard
        title="Quick Summary"
        icon="📋"
        bgColor="bg-blue-50"
        borderColor="border-blue-200"
        iconBg="bg-blue-100"
        titleColor="text-blue-900"
        textColor="text-blue-800"
      >
        {bill.tldr ? (
          <>
            <p className="leading-relaxed">{bill.tldr}</p>
            <p className="text-sm text-blue-600 mt-2 italic">Written for 8th grade reading level</p>
          </>
        ) : bill.simple_summary ? (
          <p className="leading-relaxed">{bill.simple_summary}</p>
        ) : (
          <p className="leading-relaxed text-gray-500">No summary available</p>
        )}
      </BillDetailCard>

      {/* What This Bill Does */}
      {bill.summary_what_does && (
        <BillDetailCard
          title={bill.summary_what_does.title || "What This Bill Does"}
          icon="⚖️"
          bgColor="bg-indigo-50"
          borderColor="border-indigo-200"
          iconBg="bg-indigo-100"
          titleColor="text-indigo-900"
          textColor="text-indigo-800"
        >
          <p className="leading-relaxed mb-3">{bill.summary_what_does.content}</p>
          {bill.summary_what_does.key_points && bill.summary_what_does.key_points.length > 0 && (
            <div className="space-y-2">
              {bill.summary_what_does.key_points.map((point, index) => (
                <div key={index} className="flex items-start gap-2">
                  <div className="w-1.5 h-1.5 rounded-full bg-indigo-400 mt-2 flex-shrink-0"></div>
                  <p className="text-sm">{point}</p>
                </div>
              ))}
            </div>
          )}
        </BillDetailCard>
      )}

      {/* Who This Affects */}
      {bill.summary_who_affects && (
        <BillDetailCard
          title={bill.summary_who_affects.title || "Who This Affects"}
          icon="👥"
          bgColor="bg-green-50"
          borderColor="border-green-200"
          iconBg="bg-green-100"
          titleColor="text-green-900"
          textColor="text-green-800"
        >
          <p className="leading-relaxed mb-4">{bill.summary_who_affects.content}</p>
          {bill.summary_who_affects.affected_groups && bill.summary_who_affects.affected_groups.length > 0 && (
            <div className="flex flex-wrap gap-2">
              {bill.summary_who_affects.affected_groups.map((group, index) => (
                <span key={index} className="bg-green-200 text-green-800 px-3 py-1 rounded-full text-xs font-medium">
                  {group}
                </span>
              ))}
            </div>
          )}
        </BillDetailCard>
      )}

      {/* Why It Matters */}
      {bill.summary_why_matters && (
        <BillDetailCard
          title={bill.summary_why_matters.title || "Why It Matters"}
          icon="💡"
          bgColor="bg-purple-50"
          borderColor="border-purple-200"
          iconBg="bg-purple-100"
          titleColor="text-purple-900"
          textColor="text-purple-800"
        >
          <p className="leading-relaxed mb-4">{bill.summary_why_matters.content}</p>
          
          {bill.summary_why_matters.benefits && bill.summary_why_matters.benefits.length > 0 && (
            <div className="mb-4">
              <h5 className="font-semibold text-green-700 mb-2 text-sm">✅ Potential Benefits:</h5>
              <div className="space-y-1">
                {bill.summary_why_matters.benefits.map((benefit, index) => (
                  <div key={index} className="flex items-start gap-2">
                    <div className="w-1.5 h-1.5 rounded-full bg-green-400 mt-2 flex-shrink-0"></div>
                    <p className="text-sm text-green-700">{benefit}</p>
                  </div>
                ))}
              </div>
            </div>
          )}

          {bill.summary_why_matters.concerns && bill.summary_why_matters.concerns.length > 0 && (
            <div>
              <h5 className="font-semibold text-red-700 mb-2 text-sm">⚠️ Potential Concerns:</h5>
              <div className="space-y-1">
                {bill.summary_why_matters.concerns.map((concern, index) => (
                  <div key={index} className="flex items-start gap-2">
                    <div className="w-1.5 h-1.5 rounded-full bg-red-400 mt-2 flex-shrink-0"></div>
                    <p className="text-sm text-red-700">{concern}</p>
                  </div>
                ))}
              </div>
            </div>
          )}
        </BillDetailCard>
      )}





      {/* Summary Version Tracker */}
      {summaryVersions.length > 0 && (
        <SummaryVersionTracker
          versions={summaryVersions}
          className="mt-6"
        />
      )}
    </div>
  );
}

// Reusable Bill Detail Card Component
function BillDetailCard({ 
  title, 
  icon, 
  bgColor, 
  borderColor, 
  iconBg, 
  titleColor, 
  textColor, 
  children 
}: {
  title: string;
  icon: string;
  bgColor: string;
  borderColor: string;
  iconBg: string;
  titleColor: string;
  textColor: string;
  children: React.ReactNode;
}) {
  return (
    <div className={`${bgColor} rounded-xl p-6 border ${borderColor}`}>
      <div className="flex items-start gap-4">
        <div className={`flex-shrink-0 w-12 h-12 ${iconBg} rounded-full flex items-center justify-center`}>
          <span className="text-2xl">{icon}</span>
        </div>
        <div className="flex-1 min-w-0">
          <h4 className={`heading-3 ${titleColor} mb-3`}>
            {title}
          </h4>
          <div className={`${textColor}`}>
            {children}
          </div>
        </div>
      </div>
    </div>
  );
}

// Step Components
function StanceSelectionStep({ 
  watchedStance, 
  onStanceSelect 
}: { 
  watchedStance: string | undefined; 
  onStanceSelect: (stance: 'support' | 'oppose' | 'amend') => void; 
}) {
  return (
    <div className="space-y-8">
      <div className="text-center">
        <h2 className="heading-1 text-gray-900 mb-4">What's your position?</h2>
        <p className="body-base text-gray-600 max-w-2xl mx-auto">
          Choose your stance on this legislation. We'll use this to craft a personalized message to your representatives.
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto">
        {[
          { 
            key: 'support', 
            title: 'Support', 
            description: 'I want this bill to pass as written',
            icon: '👍',
            gradient: 'from-green-500 to-emerald-600',
            hoverGradient: 'from-green-600 to-emerald-700'
          },
          { 
            key: 'oppose', 
            title: 'Oppose', 
            description: 'I don\'t want this bill to pass',
            icon: '👎',
            gradient: 'from-red-500 to-rose-600',
            hoverGradient: 'from-red-600 to-rose-700'
          },
          { 
            key: 'amend', 
            title: 'Needs Changes', 
            description: 'I support with modifications',
            icon: '✏️',
            gradient: 'from-amber-500 to-orange-600',
            hoverGradient: 'from-amber-600 to-orange-700'
          }
        ].map((stance) => (
          <button
            key={stance.key}
            type="button"
            onClick={() => onStanceSelect(stance.key as 'support' | 'oppose' | 'amend')}
            className={`relative overflow-hidden rounded-2xl p-8 text-left transition-all duration-300 transform hover:scale-105 focus-ring ${
              watchedStance === stance.key
                ? 'ring-4 ring-blue-500 ring-opacity-50'
                : ''
            }`}
          >
            <div className={`absolute inset-0 bg-gradient-to-br ${
              watchedStance === stance.key ? stance.hoverGradient : stance.gradient
            } transition-all duration-300`}></div>
            
            <div className="relative">
              <div className="flex items-center justify-between mb-6">
                <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center text-3xl backdrop-blur-sm">
                  {stance.icon}
                </div>
                {watchedStance === stance.key && (
                  <CheckIcon className="w-8 h-8 text-white animate-fade-in" />
                )}
              </div>
              
              <h3 className="heading-2 text-white mb-3">{stance.title}</h3>
              <p className="text-white/90 body-sm leading-relaxed">{stance.description}</p>
            </div>
          </button>
        ))}
      </div>
    </div>
  );
}

function ReasonsStep({ 
  watchedStance, 
  watchedReasons, 
  watchedCustomReasons, 
  customReasonInput, 
  setCustomReasonInput, 
  availableReasons, 
  onReasonToggle, 
  onAddCustomReason, 
  register 
}: any) {
  return (
    <div className="space-y-8">
      <div className="text-center">
        <h2 className="heading-1 text-gray-900 mb-4">
          Why do you {watchedStance} this bill?
        </h2>
        <p className="body-base text-gray-600 max-w-2xl mx-auto">
          Share your reasons to make your message more impactful. This step is optional but recommended.
        </p>
      </div>

      {/* Predefined Reasons */}
      {availableReasons.length > 0 && (
        <div className="space-y-4">
          <h3 className="heading-3 text-gray-900">Select reasons that apply</h3>
          <div className="grid grid-cols-1 gap-3">
            {availableReasons.map((reason: string, index: number) => (
              <label
                key={index}
                className="flex items-start p-4 border border-gray-200 rounded-xl hover:bg-gray-50 cursor-pointer transition-colors"
              >
                <input
                  type="checkbox"
                  checked={watchedReasons.includes(reason)}
                  onChange={() => onReasonToggle(reason)}
                  className="mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <span className="ml-3 body-sm text-gray-700">{reason}</span>
              </label>
            ))}
          </div>
        </div>
      )}

      {/* Custom Reasons */}
      <div className="space-y-4">
        <h3 className="heading-3 text-gray-900">Add your own reasons</h3>
        <div className="flex gap-3">
          <input
            type="text"
            value={customReasonInput}
            onChange={(e) => setCustomReasonInput(e.target.value)}
            placeholder="Enter your custom reason..."
            className="form-input flex-1"
            onKeyDown={(e) => {
              if (e.key === 'Enter') {
                e.preventDefault();
                e.stopPropagation(); // Prevent event from bubbling up to form
                onAddCustomReason();
              }
            }}
          />
          <button
            type="button"
            onClick={onAddCustomReason}
            disabled={!customReasonInput.trim()}
            className="btn-primary px-6"
          >
            Add
          </button>
        </div>

        {watchedCustomReasons.length > 0 && (
          <div className="space-y-2">
            {watchedCustomReasons.map((reason: string, index: number) => (
              <div
                key={index}
                className="flex items-center justify-between p-3 bg-blue-50 border border-blue-200 rounded-lg"
              >
                <span className="body-sm text-blue-900">{reason}</span>
                <button
                  type="button"
                  onClick={() => {
                    // Remove custom reason logic would go here
                  }}
                  className="text-blue-600 hover:text-blue-800"
                >
                  ×
                </button>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Personal Story */}
      <div className="space-y-4">
        <h3 className="heading-3 text-gray-900">Share your personal story (optional)</h3>
        <p className="body-sm text-gray-600">
          Personal stories make your message more compelling. Share how this bill affects you, your family, or your community.
        </p>
        <textarea
          {...register('personal_stories')}
          rows={4}
          placeholder="Example: As a small business owner, this bill would help me provide better healthcare for my employees..."
          className="form-textarea"
        />
      </div>
    </div>
  );
}

function ContactStep({ user, register, errors }: any) {
  return (
    <div className="space-y-8">
      <div className="text-center">
        <h2 className="heading-1 text-gray-900 mb-4">Your Contact Information</h2>
        <p className="body-base text-gray-600 max-w-2xl mx-auto">
          We need your contact details to send your message to the right representatives and confirm delivery.
        </p>
      </div>

      <div className="max-w-2xl mx-auto space-y-6">
        {!user && (
          <div className="bg-blue-50 border border-blue-200 rounded-xl p-6 text-center">
            <h3 className="heading-3 text-blue-900 mb-2">Sign in for a better experience</h3>
            <p className="body-sm text-blue-700 mb-4">
              Save your information and track your advocacy history
            </p>
            <LoginButton 
              className="btn-primary bg-blue-600 hover:bg-blue-700"
              preserveState={true}
              stateKey={`bill-action-form-${billId}`}
              onSaveState={saveFormState}
            >
              Sign In / Sign Up
            </LoginButton>
            <p className="body-sm text-blue-600 mt-3">or continue as guest below</p>
          </div>
        )}

        {/* Name Fields */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="form-label">
              First Name *
            </label>
            <input
              {...register('first_name', {
                required: 'First name is required'
              })}
              type="text"
              placeholder="John"
              defaultValue={user?.given_name || ""}
              className="form-input"
            />
            {errors.first_name && (
              <p className="form-error">{errors.first_name.message}</p>
            )}
          </div>

          <div>
            <label className="form-label">
              Last Name *
            </label>
            <input
              {...register('last_name', {
                required: 'Last name is required'
              })}
              type="text"
              placeholder="Doe"
              defaultValue={user?.family_name || ""}
              className="form-input"
            />
            {errors.last_name && (
              <p className="form-error">{errors.last_name.message}</p>
            )}
          </div>
        </div>

        {/* Email and ZIP */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="form-label">
              Email Address *
            </label>
            <input
              {...register('email', {
                required: 'Email address is required',
                pattern: {
                  value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                  message: 'Please enter a valid email address'
                }
              })}
              type="email"
              placeholder={user?.email || "<EMAIL>"}
              defaultValue={user?.email || ""}
              className="form-input"
            />
            {errors.email && (
              <p className="form-error">{errors.email.message}</p>
            )}
          </div>

          <div>
            <label className="form-label">
              ZIP Code *
            </label>
            <input
              {...register('zip_code', {
                required: 'ZIP code is required',
                pattern: {
                  value: /^\d{5}(-\d{4})?$/,
                  message: 'Please enter a valid ZIP code'
                }
              })}
              type="text"
              placeholder="12345"
              className="form-input"
            />
            {errors.zip_code && (
              <p className="form-error">{errors.zip_code.message}</p>
            )}
          </div>
        </div>

        {/* Address */}
        <div>
          <label className="form-label">
            Street Address *
          </label>
          <input
            {...register('address', {
              required: 'Street address is required'
            })}
            type="text"
            placeholder="123 Main St"
            className="form-input"
          />
          {errors.address && (
            <p className="form-error">{errors.address.message}</p>
          )}
        </div>

        {/* City and State */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="form-label">
              City *
            </label>
            <input
              {...register('city', {
                required: 'City is required'
              })}
              type="text"
              placeholder="San Francisco"
              className="form-input"
            />
            {errors.city && (
              <p className="form-error">{errors.city.message}</p>
            )}
          </div>

          <div>
            <label className="form-label">
              State *
            </label>
            <input
              {...register('state', {
                required: 'State is required'
              })}
              type="text"
              placeholder="CA"
              className="form-input"
            />
            {errors.state && (
              <p className="form-error">{errors.state.message}</p>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

function AIGenerationStep({ isLoadingPreview, aiProgress, aiProgressMessage, messagePreview }: any) {
  return (
    <div className="space-y-8">
      <div className="text-center">
        <h2 className="heading-1 text-gray-900 mb-4">Crafting Your Message</h2>
        <p className="body-base text-gray-600 max-w-2xl mx-auto">
          Our AI is creating a personalized message based on your position and story.
        </p>
      </div>

      <div className="max-w-2xl mx-auto">
        {isLoadingPreview ? (
          <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-2xl p-8 text-center">
            <div className="flex items-center justify-center mb-6">
              <SparklesIcon className="w-12 h-12 text-blue-600 animate-spin" />
            </div>
            
            <h3 className="heading-2 text-blue-900 mb-4">AI is writing your letter...</h3>
            
            <div className="w-full bg-blue-200 rounded-full h-3 mb-4">
              <div
                className="bg-blue-600 h-3 rounded-full transition-all duration-300 ease-out"
                style={{ width: `${aiProgress}%` }}
              ></div>
            </div>
            
            <p className="body-sm text-blue-800 mb-6">{aiProgressMessage}</p>
            
            <div className="text-left space-y-2 body-sm text-blue-700">
              <p>✨ Finding your representatives</p>
              <p>🎯 Analyzing your position and story</p>
              <p>📝 Crafting personalized messages</p>
            </div>
          </div>
        ) : messagePreview ? (
          <div className="bg-green-50 border border-green-200 rounded-2xl p-8 text-center">
            <CheckIcon className="w-12 h-12 text-green-600 mx-auto mb-4" />
            <h3 className="heading-2 text-green-900 mb-2">Message Ready!</h3>
            <p className="body-base text-green-700">
              Found {messagePreview.representatives?.length || 0} representative{(messagePreview.representatives?.length || 0) > 1 ? 's' : ''} for your area.
              Your personalized message is ready for review.
            </p>
          </div>
        ) : (
          <div className="bg-gray-50 border border-gray-200 rounded-2xl p-8 text-center">
            <p className="body-base text-gray-600">Waiting to generate your message...</p>
          </div>
        )}
      </div>
    </div>
  );
}

function EditAndSendStep({ messagePreview, register, errors, user, isSubmitting, watchedStance, watchedReasons, watchedCustomReasons, getValues, setValue }: any) {
  console.log('🎯 EditAndSendStep rendered');
  
  // Track component lifecycle for debugging
  React.useEffect(() => {
    console.log('🎯 EditAndSendStep mounted/updated');

    return () => {
      console.log('🎯 EditAndSendStep cleanup');
    };
  }, []);
  
  return (
    <div className="space-y-8">
      <div className="text-center">
        <h2 className="heading-1 text-gray-900 mb-4">Review & Send Your Message</h2>
        <p className="body-base text-gray-600 max-w-2xl mx-auto">
          Your personalized message is ready. You can edit it or send it as-is.
        </p>
      </div>

      <div className="max-w-4xl mx-auto space-y-8">
        {/* Recipients */}
        <div className="bg-blue-50 border border-blue-200 rounded-xl p-6">
          <h3 className="heading-3 text-blue-900 mb-4">Your Representatives</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {messagePreview.representatives?.map((rep: any, index: number) => (
              <div key={index} className="bg-white rounded-lg p-4">
                <p className="font-medium text-gray-900">{rep.full_name || rep.name || 'Representative'}</p>
                <p className="text-sm text-gray-600">
                  {rep.title || rep.chamber} {rep.state ? `- ${rep.state}` : ''} ({rep.party || 'Unknown Party'})
                </p>
                {rep.email && (
                  <p className="text-xs text-gray-500 mt-1">{rep.email}</p>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Message Editor */}
        <div>
          <div className="flex items-center justify-between mb-4">
            <h3 className="heading-3 text-gray-900">Your Message</h3>
            {messagePreview.personalized_messages?.length > 1 && (
              <div className="text-sm text-gray-600">
                {messagePreview.personalized_messages.length} personalized versions
              </div>
            )}
          </div>
          
          {messagePreview.personalized_messages?.length > 1 ? (
            <div className="space-y-4">
              <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
                <p className="text-sm text-amber-800">
                  📝 Multiple personalized messages were generated for your different representatives. 
                  Edit the message below - it will be used as the template for all representatives.
                </p>
              </div>
              
              <textarea
                {...register('custom_message')}
                rows={12}
                className="form-textarea"
                placeholder="Your personalized message will appear here..."
              />
              
              <details className="border border-gray-200 rounded-lg">
                <summary className="p-3 text-sm font-medium text-gray-700 cursor-pointer hover:bg-gray-50">
                  View individual messages ({messagePreview.personalized_messages.length})
                </summary>
                <div className="border-t border-gray-200 p-4 space-y-4">
                  {messagePreview.personalized_messages.map((msg: any, index: number) => (
                    <div key={index} className="border border-gray-100 rounded p-3">
                      <h5 className="font-medium text-gray-900 mb-2">
                        To: {msg.representative?.full_name || msg.representative?.name || `Representative ${index + 1}`}
                      </h5>
                      <p className="text-sm text-gray-700 whitespace-pre-wrap">{msg.body}</p>
                    </div>
                  ))}
                </div>
              </details>
            </div>
          ) : (
            <div>
              <textarea
                {...register('custom_message')}
                rows={12}
                className="form-textarea"
                placeholder="Your personalized message will appear here..."
                onKeyDown={(e) => {
                  // Prevent any form submission from textarea
                  if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
                    console.log('🚨 BLOCKED: Ctrl+Enter in textarea prevented form submission');
                    e.preventDefault();
                    e.stopPropagation();
                  }
                  if (e.key === 'Enter' && e.shiftKey === false && e.ctrlKey === false && e.metaKey === false) {
                    // Allow normal Enter for line breaks, but prevent any bubbling
                    e.stopPropagation();
                  }
                }}
                onKeyPress={(e) => {
                  // Additional safety: prevent any key press from bubbling to form
                  e.stopPropagation();
                }}
              />
            </div>
          )}
          
          <p className="mt-2 body-sm text-gray-500">
            This message will be sent to all your representatives. Make any edits you'd like.
          </p>
        </div>

        {/* Summary */}
        <div className="bg-green-50 border border-green-200 rounded-xl p-6">
          <h3 className="heading-3 text-green-900 mb-4">Action Summary</h3>
          <div className="space-y-2 body-sm text-green-800">
            <p><strong>Position:</strong> {watchedStance?.charAt(0).toUpperCase() + watchedStance?.slice(1)} this bill</p>
            <p><strong>Recipients:</strong> {messagePreview.representatives?.length || 0} representative{(messagePreview.representatives?.length || 0) > 1 ? 's' : ''}</p>
            <p><strong>Your reasons:</strong> {[...watchedReasons, ...watchedCustomReasons].length > 0 ? `${[...watchedReasons, ...watchedCustomReasons].length} selected` : 'Using general position'}</p>
            {getValues('personal_stories') && (
              <p><strong>Personal story:</strong> ✓ Included in your message</p>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

