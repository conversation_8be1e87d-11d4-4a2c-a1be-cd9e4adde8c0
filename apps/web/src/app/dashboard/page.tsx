'use client';

import React, { useState, useEffect } from 'react';
import { useUser } from '@auth0/nextjs-auth0/client';
import { toast } from 'react-hot-toast';

interface UserAction {
  id: string;
  bill_id: string;
  bill_title: string;
  bill_status: string;
  bill_summary: string;
  stance: string;
  action_status: string;
  subject: string;
  message: string;
  created_at: string;
  sent_at?: string;
  delivered_at?: string;
  selected_reasons: Array<{
    id: string;
    reason_text: string;
  }>;
  custom_reason?: string;
  representatives_contacted: Array<{
    name: string;
    title: string;
    party: string;
  }>;
  delivery_method?: string;
  error_message?: string;
}

interface ActionHistoryResponse {
  actions: UserAction[];
  total_count: number;
  limit: number;
  offset: number;
}

export default function DashboardPage() {
  const { user, isLoading: userLoading } = useUser();
  const [actions, setActions] = useState<UserAction[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedAction, setSelectedAction] = useState<string | null>(null);
  const [totalCount, setTotalCount] = useState(0);
  const [currentPage, setCurrentPage] = useState(0);
  const itemsPerPage = 10;

  useEffect(() => {
    if (user?.sub) {
      loadUserActions();
    }
  }, [user?.sub, currentPage]);

  const loadUserActions = async () => {
    if (!user?.sub) return;
    
    setLoading(true);
    try {
      const offset = currentPage * itemsPerPage;
      const response = await fetch(
        `http://localhost:8000/api/v1/simple/user/${user.sub}/actions?limit=${itemsPerPage}&offset=${offset}`
      );
      
      if (response.ok) {
        const data: ActionHistoryResponse = await response.json();
        setActions(data.actions);
        setTotalCount(data.total_count);
      } else {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
    } catch (error) {
      console.error('Error loading user actions:', error);
      toast.error('Failed to load your action history');
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'sent':
      case 'delivered':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStanceColor = (stance: string) => {
    switch (stance.toLowerCase()) {
      case 'support':
        return 'bg-green-100 text-green-800';
      case 'oppose':
        return 'bg-red-100 text-red-800';
      case 'amend':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getBillStatusColor = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'passed':
        return 'bg-green-100 text-green-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      case 'active':
      case 'introduced':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const totalPages = Math.ceil(totalCount / itemsPerPage);

  if (userLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Please Sign In</h2>
          <p className="text-gray-600">You need to be signed in to view your action history.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Your Action History</h1>
          <p className="mt-2 text-gray-600">
            Track your messages to representatives and follow bill progress
          </p>
        </div>

        {/* Summary Stats */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <div className="bg-white p-6 rounded-lg shadow-sm">
            <div className="text-2xl font-bold text-blue-600">{totalCount}</div>
            <div className="text-sm text-gray-600">Total Actions Taken</div>
          </div>
          <div className="bg-white p-6 rounded-lg shadow-sm">
            <div className="text-2xl font-bold text-green-600">
              {actions.filter(a => a.action_status === 'sent' || a.action_status === 'delivered').length}
            </div>
            <div className="text-sm text-gray-600">Messages Sent</div>
          </div>
          <div className="bg-white p-6 rounded-lg shadow-sm">
            <div className="text-2xl font-bold text-purple-600">
              {new Set(actions.map(a => a.bill_id)).size}
            </div>
            <div className="text-sm text-gray-600">Bills Engaged With</div>
          </div>
        </div>

        {/* Actions List */}
        <div className="bg-white shadow-sm rounded-lg overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900">Your Messages</h2>
          </div>

          {loading ? (
            <div className="text-center py-12">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
              <p className="text-gray-600 mt-4">Loading your actions...</p>
            </div>
          ) : actions.length === 0 ? (
            <div className="text-center py-12">
              <p className="text-gray-600">You haven't taken any actions yet.</p>
              <a
                href="/bills"
                className="mt-4 inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700"
              >
                Find Bills to Support
              </a>
            </div>
          ) : (
            <div className="divide-y divide-gray-200">
              {actions.map((action) => (
                <div key={action.id} className="p-6">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      {/* Bill Title and Status */}
                      <div className="flex items-center mb-2">
                        <h3 className="text-lg font-medium text-gray-900 mr-3">
                          {action.bill_title}
                        </h3>
                        <span className={`px-2 py-1 text-xs rounded-full ${getBillStatusColor(action.bill_status)}`}>
                          {action.bill_status || 'Unknown'}
                        </span>
                      </div>

                      {/* Action Summary */}
                      <div className="flex items-center space-x-4 text-sm text-gray-600 mb-3">
                        <span className={`px-2 py-1 text-xs rounded-full ${getStanceColor(action.stance)}`}>
                          {action.stance}
                        </span>
                        <span className={`px-2 py-1 text-xs rounded-full ${getStatusColor(action.action_status)}`}>
                          {action.action_status}
                        </span>
                        <span>{new Date(action.created_at).toLocaleDateString()}</span>
                        {action.representatives_contacted.length > 0 && (
                          <span>{action.representatives_contacted.length} representatives contacted</span>
                        )}
                      </div>

                      {/* Message Subject */}
                      <p className="text-sm font-medium text-gray-800 mb-2">
                        Subject: {action.subject}
                      </p>

                      {/* Error Message */}
                      {action.error_message && (
                        <div className="bg-red-50 border border-red-200 rounded-md p-3 mb-3">
                          <p className="text-sm text-red-700">
                            <strong>Error:</strong> {action.error_message}
                          </p>
                        </div>
                      )}

                      {/* Representatives */}
                      {action.representatives_contacted.length > 0 && (
                        <div className="mb-3">
                          <h4 className="text-sm font-medium text-gray-700 mb-1">Representatives Contacted:</h4>
                          <div className="flex flex-wrap gap-2">
                            {action.representatives_contacted.map((rep, idx) => (
                              <span key={idx} className="text-xs bg-gray-100 text-gray-700 px-2 py-1 rounded">
                                {rep.name} ({rep.party})
                              </span>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>

                    {/* Action Button */}
                    <button
                      onClick={() => setSelectedAction(selectedAction === action.id ? null : action.id)}
                      className="ml-4 text-blue-600 hover:text-blue-800 text-sm font-medium"
                    >
                      {selectedAction === action.id ? 'Hide Details' : 'View Details'}
                    </button>
                  </div>

                  {/* Expanded Details */}
                  {selectedAction === action.id && (
                    <div className="mt-4 pt-4 border-t border-gray-200 space-y-4">
                      {/* Full Message */}
                      <div>
                        <h4 className="text-sm font-medium text-gray-700 mb-2">Message Sent:</h4>
                        <div className="bg-gray-50 p-3 rounded-md">
                          <p className="text-sm text-gray-700 whitespace-pre-wrap">{action.message}</p>
                        </div>
                      </div>

                      {/* Bill Summary */}
                      {action.bill_summary && (
                        <div>
                          <h4 className="text-sm font-medium text-gray-700 mb-2">Bill Summary:</h4>
                          <div className="bg-blue-50 p-3 rounded-md">
                            <p className="text-sm text-gray-700">{action.bill_summary}</p>
                          </div>
                        </div>
                      )}

                      {/* User's Reasoning */}
                      {(action.selected_reasons.length > 0 || action.custom_reason) && (
                        <div>
                          <h4 className="text-sm font-medium text-gray-700 mb-2">Your Reasoning:</h4>
                          <div className="bg-green-50 p-3 rounded-md">
                            {action.selected_reasons.length > 0 && (
                              <div className="mb-2">
                                <p className="text-xs font-medium text-gray-600 mb-1">Selected Reasons:</p>
                                <ul className="list-disc list-inside space-y-1">
                                  {action.selected_reasons.map((reason, idx) => (
                                    <li key={idx} className="text-sm text-gray-700">{reason.reason_text}</li>
                                  ))}
                                </ul>
                              </div>
                            )}
                            {action.custom_reason && (
                              <div>
                                <p className="text-xs font-medium text-gray-600 mb-1">Your Custom Reason:</p>
                                <p className="text-sm text-gray-700 italic">"{action.custom_reason}"</p>
                              </div>
                            )}
                          </div>
                        </div>
                      )}

                      {/* Technical Details */}
                      <div>
                        <h4 className="text-sm font-medium text-gray-700 mb-2">Delivery Details:</h4>
                        <div className="bg-gray-50 p-3 rounded-md text-sm text-gray-600">
                          <div className="grid grid-cols-2 gap-4">
                            <div>
                              <span className="font-medium">Created:</span> {new Date(action.created_at).toLocaleString()}
                            </div>
                            <div>
                              <span className="font-medium">Method:</span> {action.delivery_method || 'N/A'}
                            </div>
                            {action.sent_at && (
                              <div>
                                <span className="font-medium">Sent:</span> {new Date(action.sent_at).toLocaleString()}
                              </div>
                            )}
                            {action.delivered_at && (
                              <div>
                                <span className="font-medium">Delivered:</span> {new Date(action.delivered_at).toLocaleString()}
                              </div>
                            )}
                          </div>
                        </div>
                      </div>

                      {/* Action Links */}
                      <div className="flex space-x-3">
                        <a
                          href={`/bills/${action.bill_id}`}
                          className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                        >
                          View Bill Details →
                        </a>
                        <a
                          href={`/bills/${action.bill_id}/action`}
                          className="text-green-600 hover:text-green-800 text-sm font-medium"
                        >
                          Take Another Action →
                        </a>
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="px-6 py-4 border-t border-gray-200 flex items-center justify-between">
              <div className="text-sm text-gray-600">
                Showing {currentPage * itemsPerPage + 1} to {Math.min((currentPage + 1) * itemsPerPage, totalCount)} of {totalCount} actions
              </div>
              <div className="flex space-x-2">
                <button
                  onClick={() => setCurrentPage(Math.max(0, currentPage - 1))}
                  disabled={currentPage === 0}
                  className={`px-3 py-1 text-sm rounded-md ${
                    currentPage === 0
                      ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                      : 'bg-blue-600 text-white hover:bg-blue-700'
                  }`}
                >
                  Previous
                </button>
                <button
                  onClick={() => setCurrentPage(Math.min(totalPages - 1, currentPage + 1))}
                  disabled={currentPage >= totalPages - 1}
                  className={`px-3 py-1 text-sm rounded-md ${
                    currentPage >= totalPages - 1
                      ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                      : 'bg-blue-600 text-white hover:bg-blue-700'
                  }`}
                >
                  Next
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}